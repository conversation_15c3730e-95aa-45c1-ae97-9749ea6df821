'use client'

import {
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Eye,
  Star,
  Activity,
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  Clock
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useAuthStore } from '@/store/auth'
import { PERMISSIONS } from '@/lib/permissions'
import { RequirePermission } from '@/components/auth/permission-guard'
import { formatPrice, formatDate } from '@/lib/utils'
import { useAdminOrders } from '@/hooks/use-orders'
import { useUsers } from '@/hooks/use-users'
import { useProducts } from '@/hooks/use-products'
import { useAdminDashboard, useTopProducts, useRecentActivity } from '@/hooks/use-admin-dashboard'

// BiHub Components
import {
  BiHubAdminCard,
  BiHubStatusBadge,
} from './bihub-admin-components'
import { AdminTabs, TabContent, TabStats } from './admin-tabs'
import { BIHUB_ADMIN_THEME } from '@/constants/admin-theme'
import { cn } from '@/lib/utils'

export function AdminDashboard() {
  const { user, isAuthenticated } = useAuthStore()

  // Debug log
  console.log('AdminDashboard - Auth status:', { user, isAuthenticated })
  console.log('AdminDashboard - User role:', user?.role)

  // Fetch real data from APIs
  const { data: dashboardData, isLoading: dashboardLoading, error: dashboardError } = useAdminDashboard()
  const { data: ordersData, isLoading: ordersLoading } = useAdminOrders({ limit: 5 })
  const { data: usersData, isLoading: usersLoading } = useUsers({ limit: 100 })
  const { data: productsData, isLoading: productsLoading } = useProducts({ limit: 100 })
  const { data: topProductsData, isLoading: topProductsLoading } = useTopProducts({ limit: 5 })
  const { data: recentActivityData, isLoading: activityLoading } = useRecentActivity({ limit: 5 })

  // Debug log
  console.log('AdminDashboard - Dashboard data:', dashboardData)
  console.log('AdminDashboard - Dashboard loading:', dashboardLoading)
  console.log('AdminDashboard - Dashboard error:', dashboardError)

  // Use dashboard data if available, otherwise fallback to individual API calls
  const recentOrders = dashboardData?.recent_orders || ordersData?.data || []
  const totalUsers = dashboardData?.overview?.total_customers || usersData?.pagination?.total || 0
  const totalProducts = dashboardData?.overview?.total_products || productsData?.pagination?.total || 0
  const totalOrders = dashboardData?.overview?.total_orders || ordersData?.pagination?.total || 0
  const totalRevenue = dashboardData?.overview?.total_revenue || 0
  
  // Debug log for revenue
  console.log('AdminDashboard - Total revenue:', totalRevenue)
  console.log('AdminDashboard - Dashboard overview:', dashboardData?.overview)

  // Calculate stats with growth indicators (use real data if available)
  const stats = {
    revenue: {
      current: typeof totalRevenue === 'number' ? totalRevenue : 0,
      change: dashboardData?.revenue_growth || 15.2,
      isPositive: (dashboardData?.revenue_growth || 15.2) > 0,
    },
    orders: {
      current: typeof totalOrders === 'number' ? totalOrders : 0,
      change: dashboardData?.orders_growth || 8.5,
      isPositive: (dashboardData?.orders_growth || 8.5) > 0,
    },
    customers: {
      current: typeof totalUsers === 'number' ? totalUsers : 0,
      change: dashboardData?.customers_growth || 12.3,
      isPositive: (dashboardData?.customers_growth || 12.3) > 0,
    },
    products: {
      current: typeof totalProducts === 'number' ? totalProducts : 0,
      change: dashboardData?.products_growth || 5.7,
      isPositive: (dashboardData?.products_growth || 5.7) > 0,
    },
  }

  // Get recent activity data (use real data if available)
  const recentActivity = recentActivityData || dashboardData?.recent_activity || [
    { id: 'activity-1', type: 'order', message: 'New order received', time: '2 minutes ago', status: 'success' },
    { id: 'activity-2', type: 'user', message: 'New customer registered', time: '5 minutes ago', status: 'info' },
    { id: 'activity-3', type: 'product', message: 'Product updated', time: '10 minutes ago', status: 'warning' },
    { id: 'activity-4', type: 'order', message: 'Order shipped', time: '15 minutes ago', status: 'success' },
    { id: 'activity-5', type: 'system', message: 'System backup completed', time: '1 hour ago', status: 'info' },
  ]

  // Get top products (use real data if available)
  const topProducts = topProductsData || dashboardData?.charts?.top_products || (productsData?.data || [])
    .slice(0, 5)
    .map((product, index) => ({
      id: product.id,
      name: product.name,
      sales: Math.floor(Math.random() * 200) + 50,
      revenue: Math.floor((product.pricing?.price || 0) * (Math.floor(Math.random() * 100) + 20)),
      growth: Math.floor(Math.random() * 30) + 5,
    }))

  // Additional stats from dashboard
  const pendingOrders = dashboardData?.overview?.pending_orders || 0
  const lowStockItems = dashboardData?.overview?.low_stock_items || 0
  const pendingReviews = dashboardData?.overview?.pending_reviews || 0
  const activeUsers = dashboardData?.overview?.active_users || totalUsers

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order': return <ShoppingCart className="h-4 w-4" />
      case 'user': return <Users className="h-4 w-4" />
      case 'product': return <Package className="h-4 w-4" />
      case 'system': return <Activity className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  // Loading state
  if (dashboardLoading || ordersLoading || usersLoading || productsLoading) {
    return (
      <div className={BIHUB_ADMIN_THEME.spacing.section}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className={cn(
              BIHUB_ADMIN_THEME.components.card.base,
              'p-6 animate-pulse'
            )}>
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-xl bg-gray-700"></div>
                <div className="w-16 h-6 bg-gray-700 rounded-full"></div>
              </div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-700 rounded w-20"></div>
                <div className="h-8 bg-gray-700 rounded w-24"></div>
                <div className="h-3 bg-gray-700 rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Tab configuration
  const dashboardTabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: <BarChart3 className="h-4 w-4" />,
      content: (
        <TabContent>
          <TabStats
            stats={[
              {
                label: 'Total Revenue',
                value: formatPrice(stats.revenue.current),
                change: `${stats.revenue.isPositive ? '+' : ''}${stats.revenue.change}%`,
                trend: stats.revenue.isPositive ? 'up' : 'down',
                icon: <DollarSign className="h-4 w-4 text-[#FF9000]" />
              },
              {
                label: 'Total Orders',
                value: stats.orders.current,
                change: `${stats.orders.isPositive ? '+' : ''}${stats.orders.change}%`,
                trend: stats.orders.isPositive ? 'up' : 'down',
                icon: <ShoppingCart className="h-4 w-4 text-[#FF9000]" />
              },
              {
                label: 'Total Customers',
                value: stats.customers.current,
                change: `${stats.customers.isPositive ? '+' : ''}${stats.customers.change}%`,
                trend: stats.customers.isPositive ? 'up' : 'down',
                icon: <Users className="h-4 w-4 text-[#FF9000]" />
              },
              {
                label: 'Total Products',
                value: stats.products.current,
                change: `${stats.products.isPositive ? '+' : ''}${stats.products.change}%`,
                trend: stats.products.isPositive ? 'up' : 'down',
                icon: <Package className="h-4 w-4 text-[#FF9000]" />
              }
            ]}
          />

          {/* Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Orders */}
            <RequirePermission permission={PERMISSIONS.ORDERS_VIEW_ALL}>
              <BiHubAdminCard
                title="Recent Orders"
                subtitle="Latest orders from BiHub customers"
                icon={<ShoppingCart className="h-5 w-5 text-white" />}
                headerAction={
                  <Button
                    variant="outline"
                    size="sm"
                    className={BIHUB_ADMIN_THEME.components.button.ghost}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View All
                  </Button>
                }
              >
                <div className="space-y-4">
                  {recentOrders.length > 0 ? (
                    recentOrders.map((order: any) => (
                      <div key={order.id} className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-2xl bg-gradient-to-br from-[#FF9000] to-[#e67e00] flex items-center justify-center">
                            <span className="text-white text-sm font-bold">#{order.id?.toString().slice(-2) || '00'}</span>
                          </div>
                          <div>
                            <p className={BIHUB_ADMIN_THEME.typography.body.medium}>
                              Order #{order.id}
                            </p>
                            <p className={BIHUB_ADMIN_THEME.typography.body.small}>
                              {order.user?.first_name} {order.user?.last_name}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={BIHUB_ADMIN_THEME.typography.body.medium}>
                            {formatPrice(order.total_amount)}
                          </p>
                          <BiHubStatusBadge status={order.status}>
                            {order.status}
                          </BiHubStatusBadge>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <ShoppingCart className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                      <p className={BIHUB_ADMIN_THEME.typography.body.medium}>No recent orders</p>
                      <p className={BIHUB_ADMIN_THEME.typography.body.small}>Orders will appear here when customers make purchases</p>
                    </div>
                  )}
                </div>
              </BiHubAdminCard>
            </RequirePermission>

            {/* Top Products */}
            <BiHubAdminCard
              title="Top Products"
              subtitle="Best performing products this month"
              icon={<Star className="h-5 w-5 text-white" />}
            >
              <div className="space-y-4">
                {topProducts.length > 0 ? (
                  topProducts.map((product: any, index: number) => (
                    <div key={product.id} className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                      <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#FF9000] to-[#e67e00] flex items-center justify-center">
                        <span className="text-white text-sm font-bold">#{index + 1}</span>
                      </div>
                      <div className="flex-1">
                        <p className={BIHUB_ADMIN_THEME.typography.body.medium}>
                          {product.name}
                        </p>
                        <p className={BIHUB_ADMIN_THEME.typography.body.small}>
                          {product.sales || 0} sales
                        </p>
                      </div>
                      <div className="text-right">
                        <p className={BIHUB_ADMIN_THEME.typography.body.medium}>
                          {formatPrice(product.price)}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Package className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                    <p className={BIHUB_ADMIN_THEME.typography.body.medium}>No products found</p>
                    <p className={BIHUB_ADMIN_THEME.typography.body.small}>Add products to see top performers</p>
                  </div>
                )}
              </div>
            </BiHubAdminCard>
          </div>
        </TabContent>
      )
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: <PieChart className="h-4 w-4" />,
      content: (
        <TabContent>
          <div className="text-center py-12">
            <BarChart3 className="h-16 w-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">Analytics Dashboard</h3>
            <p className="text-gray-400">Detailed analytics and reports coming soon</p>
          </div>
        </TabContent>
      )
    },
    {
      id: 'activity',
      label: 'Activity',
      icon: <Activity className="h-4 w-4" />,
      badge: recentActivity.length,
      content: (
        <TabContent>
          <BiHubAdminCard
            title="Recent Activity"
            subtitle="Latest activities in your BiHub store"
            icon={<Activity className="h-5 w-5 text-white" />}
          >
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={activity.id || `activity-${index}`} className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <div className={cn(
                    'w-8 h-8 rounded-lg flex items-center justify-center',
                    activity.status === 'success' && 'bg-green-600',
                    activity.status === 'info' && 'bg-blue-600',
                    activity.status === 'warning' && 'bg-yellow-600',
                    activity.status === 'error' && 'bg-red-600'
                  )}>
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1">
                    <p className={BIHUB_ADMIN_THEME.typography.body.medium}>
                      {activity.message}
                    </p>
                    <p className={BIHUB_ADMIN_THEME.typography.body.small}>
                      {formatDate(activity.created_at)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </BiHubAdminCard>
        </TabContent>
      )
    }
  ]

  return (
    <div className="h-full">
      {/* Welcome Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Welcome back, {user?.first_name}!
            </h1>
            <p className="text-gray-400">
              Here's your BiHub store performance overview and key metrics
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 px-4 py-2 bg-gray-800/50 rounded-xl">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">Store Online</span>
            </div>
            <div className="text-sm text-gray-400">
              Last updated: {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
          </div>
        </div>
      </div>

      {/* Tab System */}
      <AdminTabs tabs={dashboardTabs} defaultTab="overview" />
    </div>
  )
}
