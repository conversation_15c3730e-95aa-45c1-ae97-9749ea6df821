'use client'

import { useState, ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

interface TabItem {
  id: string
  label: string
  icon?: ReactNode
  content: ReactNode
  badge?: string | number
}

interface AdminTabsProps {
  tabs: TabItem[]
  defaultTab?: string
  className?: string
}

export function AdminTabs({ tabs, defaultTab, className }: AdminTabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id)

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Tab Navigation */}
      <div className="flex items-center gap-2 p-1 bg-gray-800/30 rounded-2xl mb-6 overflow-x-auto">
        {tabs.map((tab) => (
          <Button
            key={tab.id}
            variant="ghost"
            onClick={() => setActiveTab(tab.id)}
            className={cn(
              'flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-200 whitespace-nowrap',
              activeTab === tab.id
                ? 'bg-gradient-to-r from-[#FF9000] to-[#e67e00] text-white shadow-lg'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
            )}
          >
            {tab.icon}
            <span className="font-medium">{tab.label}</span>
            {tab.badge && (
              <span className={cn(
                'px-2 py-0.5 text-xs rounded-full font-bold',
                activeTab === tab.id
                  ? 'bg-white/20 text-white'
                  : 'bg-gray-600 text-gray-300'
              )}>
                {tab.badge}
              </span>
            )}
          </Button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto">
          {activeTabContent}
        </div>
      </div>
    </div>
  )
}

// Utility component for tab content wrapper
interface TabContentProps {
  children: ReactNode
  className?: string
}

export function TabContent({ children, className }: TabContentProps) {
  return (
    <div className={cn('space-y-6', className)}>
      {children}
    </div>
  )
}

// Quick stats component for tabs
interface TabStatsProps {
  stats: Array<{
    label: string
    value: string | number
    change?: string
    trend?: 'up' | 'down' | 'neutral'
    icon?: ReactNode
  }>
}

export function TabStats({ stats }: TabStatsProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {stats.map((stat, index) => (
        <div
          key={index}
          className="bg-white/5 backdrop-blur-sm border border-gray-700/30 rounded-xl p-4 hover:bg-white/10 hover:border-gray-600/40 transition-all duration-200"
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-400 font-medium">{stat.label}</span>
            {stat.icon && (
              <div className="w-8 h-8 rounded-lg bg-[#FF9000]/20 flex items-center justify-center">
                {stat.icon}
              </div>
            )}
          </div>
          <div className="flex items-end gap-2">
            <span className="text-2xl font-bold text-white">{stat.value}</span>
            {stat.change && (
              <span className={cn(
                'text-xs font-medium px-2 py-1 rounded-full',
                stat.trend === 'up' && 'text-green-400 bg-green-400/10',
                stat.trend === 'down' && 'text-red-400 bg-red-400/10',
                stat.trend === 'neutral' && 'text-gray-400 bg-gray-400/10'
              )}>
                {stat.change}
              </span>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
