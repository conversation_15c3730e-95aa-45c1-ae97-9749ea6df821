// ===== PAGES EXPORT =====
// Complete page components that use templates and handle routing

// Main pages
export { HomePage } from './HomePage'
export { AboutPage } from './AboutPage'
export { ContactPage } from './ContactPage'
export { FAQPage } from './FAQPage'
export { TermsPage } from './TermsPage'
export { PrivacyPage } from './PrivacyPage'
export { HelpPage } from './HelpPage'
export { SupportPage } from './SupportPage'

// E-commerce pages
export { ProductsPage } from './ProductsPage'
export { ProductDetailPage } from './ProductDetailPage'
export { CategoriesPage } from './CategoriesPage'
export { CategoryPage } from './CategoryPage'
export { SearchPage } from './SearchPage'
export { CartPage } from './CartPage'
export { CheckoutPage } from './CheckoutPage'
export { WishlistPage } from './WishlistPage'
export { ComparisonPage } from './ComparisonPage'
export { OrderPage } from './OrderPage'
export { OrderHistoryPage } from './OrderHistoryPage'
export { OrderTrackingPage } from './OrderTrackingPage'
export { OrderConfirmationPage } from './OrderConfirmationPage'

// User account pages
export { ProfilePage } from './ProfilePage'
export { AccountPage } from './AccountPage'
export { AccountSettingsPage } from './AccountSettingsPage'
export { AddressBookPage } from './AddressBookPage'
export { PaymentMethodsPage } from './PaymentMethodsPage'
export { UserOrdersPage } from './UserOrdersPage'
export { UserWishlistPage } from './UserWishlistPage'
export { UserReviewsPage } from './UserReviewsPage'
export { NotificationsPage } from './NotificationsPage'
export { SecurityPage } from './SecurityPage'
export { PreferencesPage } from './PreferencesPage'

// Authentication pages
export { LoginPage } from './LoginPage'
export { RegisterPage } from './RegisterPage'
export { ForgotPasswordPage } from './ForgotPasswordPage'
export { ResetPasswordPage } from './ResetPasswordPage'
export { EmailVerificationPage } from './EmailVerificationPage'
export { TwoFactorPage } from './TwoFactorPage'
export { OAuthCallbackPage } from './OAuthCallbackPage'
export { LogoutPage } from './LogoutPage'

// Admin pages
export { AdminDashboardPage } from './AdminDashboardPage'
export { AdminProductsPage } from './AdminProductsPage'
export { AdminProductDetailPage } from './AdminProductDetailPage'
export { AdminProductCreatePage } from './AdminProductCreatePage'
export { AdminProductEditPage } from './AdminProductEditPage'
export { AdminCategoriesPage } from './AdminCategoriesPage'
export { AdminCategoryDetailPage } from './AdminCategoryDetailPage'
export { AdminCategoryCreatePage } from './AdminCategoryCreatePage'
export { AdminCategoryEditPage } from './AdminCategoryEditPage'
export { AdminOrdersPage } from './AdminOrdersPage'
export { AdminOrderDetailPage } from './AdminOrderDetailPage'
export { AdminUsersPage } from './AdminUsersPage'
export { AdminUserDetailPage } from './AdminUserDetailPage'
export { AdminUserCreatePage } from './AdminUserCreatePage'
export { AdminUserEditPage } from './AdminUserEditPage'
export { AdminSettingsPage } from './AdminSettingsPage'
export { AdminAnalyticsPage } from './AdminAnalyticsPage'
export { AdminReportsPage } from './AdminReportsPage'
export { AdminInventoryPage } from './AdminInventoryPage'
export { AdminCouponsPage } from './AdminCouponsPage'
export { AdminShippingPage } from './AdminShippingPage'
export { AdminTaxesPage } from './AdminTaxesPage'
export { AdminPaymentsPage } from './AdminPaymentsPage'
export { AdminReviewsPage } from './AdminReviewsPage'
export { AdminCustomersPage } from './AdminCustomersPage'

// Content pages
export { BlogPage } from './BlogPage'
export { BlogPostPage } from './BlogPostPage'
export { NewsPage } from './NewsPage'
export { ArticlePage } from './ArticlePage'
export { GalleryPage } from './GalleryPage'
export { VideoPage } from './VideoPage'
export { DownloadsPage } from './DownloadsPage'
export { ResourcesPage } from './ResourcesPage'
export { DocumentationPage } from './DocumentationPage'

// Special pages
export { LandingPage } from './LandingPage'
export { ComingSoonPage } from './ComingSoonPage'
export { MaintenancePage } from './MaintenancePage'
export { NotFoundPage } from './NotFoundPage'
export { ErrorPage } from './ErrorPage'
export { OfflinePage } from './OfflinePage'
export { ThankYouPage } from './ThankYouPage'
export { SuccessPage } from './SuccessPage'
export { UnauthorizedPage } from './UnauthorizedPage'
export { ForbiddenPage } from './ForbiddenPage'

// Search and filter pages
export { SearchResultsPage } from './SearchResultsPage'
export { AdvancedSearchPage } from './AdvancedSearchPage'
export { FilterPage } from './FilterPage'
export { BrandPage } from './BrandPage'
export { BrandsPage } from './BrandsPage'
export { TagPage } from './TagPage'
export { TagsPage } from './TagsPage'

// Promotional pages
export { SalePage } from './SalePage'
export { DealsPage } from './DealsPage'
export { CouponsPage } from './CouponsPage'
export { PromotionsPage } from './PromotionsPage'
export { ClearancePage } from './ClearancePage'
export { NewArrivalsPage } from './NewArrivalsPage'
export { BestSellersPage } from './BestSellersPage'
export { FeaturedPage } from './FeaturedPage'

// Review and rating pages
export { ReviewsPage } from './ReviewsPage'
export { ReviewPage } from './ReviewPage'
export { WriteReviewPage } from './WriteReviewPage'
export { RatingsPage } from './RatingsPage'
export { TestimonialsPage } from './TestimonialsPage'
export { FeedbackPage } from './FeedbackPage'

// Comparison pages
export { ComparePage } from './ComparePage'
export { VersusPage } from './VersusPage'
export { FeatureComparisonPage } from './FeatureComparisonPage'
export { PriceComparisonPage } from './PriceComparisonPage'

// Social pages
export { SocialFeedPage } from './SocialFeedPage'
export { UserProfilePage } from './UserProfilePage'
export { FollowersPage } from './FollowersPage'
export { FollowingPage } from './FollowingPage'
export { ActivityPage } from './ActivityPage'
export { TimelinePage } from './TimelinePage'

// Onboarding pages
export { OnboardingPage } from './OnboardingPage'
export { WelcomePage } from './WelcomePage'
export { TutorialPage } from './TutorialPage'
export { SetupPage } from './SetupPage'
export { GettingStartedPage } from './GettingStartedPage'

// Legal pages
export { LegalPage } from './LegalPage'
export { CookiePolicyPage } from './CookiePolicyPage'
export { DisclaimerPage } from './DisclaimerPage'
export { RefundPolicyPage } from './RefundPolicyPage'
export { ShippingPolicyPage } from './ShippingPolicyPage'
export { ReturnPolicyPage } from './ReturnPolicyPage'

// Utility pages
export { SitemapPage } from './SitemapPage'
export { RobotsPage } from './RobotsPage'
export { ManifestPage } from './ManifestPage'
export { ServiceWorkerPage } from './ServiceWorkerPage'

// API pages
export { APIDocumentationPage } from './APIDocumentationPage'
export { APIReferencePage } from './APIReferencePage'
export { WebhooksPage } from './WebhooksPage'
export { IntegrationsPage } from './IntegrationsPage'

// Developer pages
export { DeveloperPage } from './DeveloperPage'
export { PlaygroundPage } from './PlaygroundPage'
export { SandboxPage } from './SandboxPage'
export { TestingPage } from './TestingPage'

// Accessibility pages
export { AccessibilityPage } from './AccessibilityPage'
export { AccessibilityStatementPage } from './AccessibilityStatementPage'
export { KeyboardShortcutsPage } from './KeyboardShortcutsPage'

// Internationalization pages
export { LanguagePage } from './LanguagePage'
export { LocalizationPage } from './LocalizationPage'
export { TranslationPage } from './TranslationPage'

// Performance pages
export { PerformancePage } from './PerformancePage'
export { OptimizationPage } from './OptimizationPage'
export { CachePage } from './CachePage'

// Analytics pages
export { AnalyticsPage } from './AnalyticsPage'
export { MetricsPage } from './MetricsPage'
export { InsightsPage } from './InsightsPage'
export { DashboardPage } from './DashboardPage'

// Security pages
export { SecuritySettingsPage } from './SecuritySettingsPage'
export { TwoFactorSetupPage } from './TwoFactorSetupPage'
export { SessionsPage } from './SessionsPage'
export { AuditLogPage } from './AuditLogPage'

// Subscription pages
export { SubscriptionPage } from './SubscriptionPage'
export { BillingPage } from './BillingPage'
export { InvoicesPage } from './InvoicesPage'
export { PlanPage } from './PlanPage'
export { UpgradePage } from './UpgradePage'

// Integration pages
export { IntegrationPage } from './IntegrationPage'
export { ConnectPage } from './ConnectPage'
export { SyncPage } from './SyncPage'
export { ImportPage } from './ImportPage'
export { ExportPage } from './ExportPage'

// Type exports
export type { HomePageProps } from './HomePage'
export type { ProductsPageProps } from './ProductsPage'
export type { ProductDetailPageProps } from './ProductDetailPage'
export type { CategoryPageProps } from './CategoryPage'
export type { SearchPageProps } from './SearchPage'
export type { CartPageProps } from './CartPage'
export type { CheckoutPageProps } from './CheckoutPage'
export type { ProfilePageProps } from './ProfilePage'
export type { LoginPageProps } from './LoginPage'
export type { AdminDashboardPageProps } from './AdminDashboardPage'
export type { AdminProductsPageProps } from './AdminProductsPage'
export type { ErrorPageProps } from './ErrorPage'
export type { NotFoundPageProps } from './NotFoundPage'
